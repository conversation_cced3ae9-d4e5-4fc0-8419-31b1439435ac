"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolRegistry = void 0;
const events_1 = require("events");
const nanoid_1 = require("nanoid");
const logger_1 = require("@/utils/logger");
const shell_1 = require("./shell");
const files_1 = require("./files");
const system_1 = require("./system");
class ToolRegistry extends events_1.EventEmitter {
    tools = new Map();
    shellTool;
    fileTool;
    systemTool;
    constructor() {
        super();
        this.shellTool = new shell_1.ShellTool();
        this.fileTool = new files_1.FileTool();
        this.systemTool = new system_1.SystemTool();
    }
    async initialize() {
        try {
            await this.registerBuiltinTools();
            logger_1.logger.info('Tool registry initialized', { toolCount: this.tools.size });
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize tool registry', error);
            throw error;
        }
    }
    async registerBuiltinTools() {
        // Shell command execution
        this.registerTool({
            name: 'shell',
            description: 'Execute shell commands safely. Use for running terminal commands, scripts, and system operations.',
            parameters: {
                type: 'object',
                properties: {
                    command: {
                        type: 'string',
                        description: 'The shell command to execute',
                    },
                    args: {
                        type: 'array',
                        items: { type: 'string' },
                        description: 'Command arguments as an array',
                        default: [],
                    },
                    cwd: {
                        type: 'string',
                        description: 'Working directory for the command',
                    },
                    timeout: {
                        type: 'number',
                        description: 'Timeout in milliseconds',
                        default: 30000,
                    },
                },
                required: ['command'],
            },
            execute: async (args) => {
                return await this.shellTool.execute(args);
            },
        });
        // File reading
        this.registerTool({
            name: 'file_read',
            description: 'Read the contents of a file. Use for examining code, configuration files, or any text files.',
            parameters: {
                type: 'object',
                properties: {
                    path: {
                        type: 'string',
                        description: 'Path to the file to read',
                    },
                    encoding: {
                        type: 'string',
                        description: 'File encoding',
                        default: 'utf-8',
                    },
                },
                required: ['path'],
            },
            execute: async (args) => {
                return await this.fileTool.read(args);
            },
        });
        // File writing
        this.registerTool({
            name: 'file_write',
            description: 'Write content to a file. Use for creating or updating files with new content.',
            parameters: {
                type: 'object',
                properties: {
                    path: {
                        type: 'string',
                        description: 'Path to the file to write',
                    },
                    content: {
                        type: 'string',
                        description: 'Content to write to the file',
                    },
                    encoding: {
                        type: 'string',
                        description: 'File encoding',
                        default: 'utf-8',
                    },
                    backup: {
                        type: 'boolean',
                        description: 'Create a backup before writing',
                        default: false,
                    },
                },
                required: ['path', 'content'],
            },
            execute: async (args) => {
                return await this.fileTool.write(args);
            },
        });
        // File creation
        this.registerTool({
            name: 'file_create',
            description: 'Create a new file with content. Use for creating new files that don\'t exist.',
            parameters: {
                type: 'object',
                properties: {
                    path: {
                        type: 'string',
                        description: 'Path to the new file',
                    },
                    content: {
                        type: 'string',
                        description: 'Initial content for the file',
                        default: '',
                    },
                    encoding: {
                        type: 'string',
                        description: 'File encoding',
                        default: 'utf-8',
                    },
                },
                required: ['path'],
            },
            execute: async (args) => {
                return await this.fileTool.create(args);
            },
        });
        // File deletion
        this.registerTool({
            name: 'file_delete',
            description: 'Delete a file or directory. Use with caution for removing files.',
            parameters: {
                type: 'object',
                properties: {
                    path: {
                        type: 'string',
                        description: 'Path to the file or directory to delete',
                    },
                    recursive: {
                        type: 'boolean',
                        description: 'Delete directories recursively',
                        default: false,
                    },
                    force: {
                        type: 'boolean',
                        description: 'Force deletion without confirmation',
                        default: false,
                    },
                },
                required: ['path'],
            },
            execute: async (args) => {
                return await this.fileTool.delete(args);
            },
        });
        // File moving/renaming
        this.registerTool({
            name: 'file_move',
            description: 'Move or rename a file or directory.',
            parameters: {
                type: 'object',
                properties: {
                    source: {
                        type: 'string',
                        description: 'Source path',
                    },
                    destination: {
                        type: 'string',
                        description: 'Destination path',
                    },
                },
                required: ['source', 'destination'],
            },
            execute: async (args) => {
                return await this.fileTool.move(args);
            },
        });
        // File copying
        this.registerTool({
            name: 'file_copy',
            description: 'Copy a file or directory to a new location.',
            parameters: {
                type: 'object',
                properties: {
                    source: {
                        type: 'string',
                        description: 'Source path',
                    },
                    destination: {
                        type: 'string',
                        description: 'Destination path',
                    },
                    recursive: {
                        type: 'boolean',
                        description: 'Copy directories recursively',
                        default: false,
                    },
                },
                required: ['source', 'destination'],
            },
            execute: async (args) => {
                return await this.fileTool.copy(args);
            },
        });
        // File searching with glob patterns
        this.registerTool({
            name: 'file_search',
            description: 'Search for files using glob patterns. Use for finding files by name or pattern.',
            parameters: {
                type: 'object',
                properties: {
                    pattern: {
                        type: 'string',
                        description: 'Glob pattern to search for',
                    },
                    cwd: {
                        type: 'string',
                        description: 'Directory to search in',
                    },
                    maxDepth: {
                        type: 'number',
                        description: 'Maximum search depth',
                        default: 10,
                    },
                },
                required: ['pattern'],
            },
            execute: async (args) => {
                return await this.fileTool.search(args);
            },
        });
        // Text searching with grep
        this.registerTool({
            name: 'grep',
            description: 'Search for text patterns in files. Use for finding specific content in files.',
            parameters: {
                type: 'object',
                properties: {
                    pattern: {
                        type: 'string',
                        description: 'Text pattern to search for',
                    },
                    files: {
                        type: 'array',
                        items: { type: 'string' },
                        description: 'Files to search in',
                    },
                    recursive: {
                        type: 'boolean',
                        description: 'Search recursively in directories',
                        default: false,
                    },
                    ignoreCase: {
                        type: 'boolean',
                        description: 'Case-insensitive search',
                        default: false,
                    },
                    lineNumbers: {
                        type: 'boolean',
                        description: 'Show line numbers',
                        default: true,
                    },
                },
                required: ['pattern', 'files'],
            },
            execute: async (args) => {
                return await this.fileTool.grep(args);
            },
        });
        // System information
        this.registerTool({
            name: 'system_info',
            description: 'Get system information including OS, hardware, and environment details.',
            parameters: {
                type: 'object',
                properties: {
                    type: {
                        type: 'string',
                        enum: ['basic', 'detailed', 'processes', 'network'],
                        description: 'Type of system information to retrieve',
                        default: 'basic',
                    },
                },
            },
            execute: async (args) => {
                return await this.systemTool.getInfo(args);
            },
        });
    }
    registerTool(tool) {
        this.tools.set(tool.name, tool);
        logger_1.logger.debug('Tool registered', { name: tool.name });
    }
    unregisterTool(name) {
        const removed = this.tools.delete(name);
        if (removed) {
            logger_1.logger.debug('Tool unregistered', { name });
        }
        return removed;
    }
    getTool(name) {
        return this.tools.get(name);
    }
    getAvailableTools() {
        return Array.from(this.tools.values()).map(tool => ({
            name: tool.name,
            description: tool.description,
            parameters: tool.parameters,
        }));
    }
    async executeTool(toolCall) {
        const startTime = Date.now();
        const tool = this.tools.get(toolCall.name);
        if (!tool) {
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: toolCall.id,
                success: false,
                output: '',
                error: `Tool not found: ${toolCall.name}`,
                executionTime: Date.now() - startTime,
            };
        }
        try {
            logger_1.logger.debug('Executing tool', {
                name: toolCall.name,
                args: toolCall.arguments
            });
            const result = await tool.execute(toolCall.arguments);
            logger_1.logger.debug('Tool execution completed', {
                name: toolCall.name,
                success: result.success,
                executionTime: result.executionTime
            });
            this.emit('tool-executed', result);
            return result;
        }
        catch (error) {
            const errorResult = {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: toolCall.id,
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
            logger_1.logger.error('Tool execution failed', {
                name: toolCall.name,
                error: errorResult.error
            });
            this.emit('tool-error', { toolCall, error: errorResult });
            return errorResult;
        }
    }
    async shutdown() {
        this.tools.clear();
        this.removeAllListeners();
        logger_1.logger.info('Tool registry shutdown');
    }
}
exports.ToolRegistry = ToolRegistry;
//# sourceMappingURL=registry.js.map