import type { AgentConfig, ConversationMessage, ToolCall, ToolResult, SessionContext, StreamChunk, AIProvider } from '@/types';
export interface AIProviderConfig {
    provider: AIProvider;
    model: string;
    apiKey?: string;
    baseUrl?: string;
    maxTokens?: number;
    temperature?: number;
}
export interface CompletionOptions {
    messages: ConversationMessage[];
    tools: any[];
    context: SessionContext;
    onChunk?: (chunk: StreamChunk) => void;
    onToolCall?: (toolCall: ToolCall) => Promise<ToolResult>;
}
export declare class AIProviderManager {
    private client;
    private config;
    constructor(config: AgentConfig);
    private createClient;
    updateConfig(newConfig: AgentConfig): Promise<void>;
    streamCompletion(options: CompletionOptions): Promise<ConversationMessage>;
    private getFinalResponse;
    private buildSystemMessage;
    private convertMessages;
    private convertMessage;
    private convertTools;
    testConnection(): Promise<boolean>;
    getConfig(): AIProviderConfig;
}
//# sourceMappingURL=providers.d.ts.map