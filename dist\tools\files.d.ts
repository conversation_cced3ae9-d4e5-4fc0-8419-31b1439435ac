import type { ToolResult } from '@/types';
export declare class FileTool {
    read(args: Record<string, unknown>): Promise<ToolResult>;
    write(args: Record<string, unknown>): Promise<ToolResult>;
    create(args: Record<string, unknown>): Promise<ToolResult>;
    delete(args: Record<string, unknown>): Promise<ToolResult>;
    move(args: Record<string, unknown>): Promise<ToolResult>;
    copy(args: Record<string, unknown>): Promise<ToolResult>;
    search(args: Record<string, unknown>): Promise<ToolResult>;
    grep(args: Record<string, unknown>): Promise<ToolResult>;
    private copyDirectory;
    private searchInFile;
}
//# sourceMappingURL=files.d.ts.map