import { EventEmitter } from 'events';
import type { SessionContext } from '@/types';
export declare class ContextManager extends EventEmitter {
    private workingDirectory;
    private watcher;
    private context;
    private lastUpdate;
    constructor(workingDirectory: string);
    initialize(): Promise<void>;
    getContext(): Promise<SessionContext>;
    updateContext(): Promise<void>;
    setWorkingDirectory(directory: string): Promise<void>;
    private discoverProjectStructure;
    private detectProjectType;
    private detectNodePackageManager;
    private scanFiles;
    private parsePackageInfo;
    private gatherEnvironmentInfo;
    private updateActiveProcesses;
    private startFileWatcher;
    private readJsonFile;
    private readTomlFile;
    private executeCommand;
    shutdown(): Promise<void>;
}
//# sourceMappingURL=context.d.ts.map