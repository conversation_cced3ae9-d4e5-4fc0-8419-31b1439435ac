import type { ToolResult } from '@/types';
export declare class ShellTool {
    private activeProcesses;
    execute(args: Record<string, unknown>): Promise<ToolResult>;
    private parseCommand;
    private parseCommandString;
    private shouldUseShell;
    private executeCommand;
    private formatOutput;
    isCommandSafe(command: string): boolean;
    getCommandRiskLevel(command: string): 'safe' | 'low' | 'medium' | 'high' | 'critical';
    killProcess(processId: string): boolean;
    killAllProcesses(): void;
    getActiveProcesses(): string[];
}
//# sourceMappingURL=shell.d.ts.map