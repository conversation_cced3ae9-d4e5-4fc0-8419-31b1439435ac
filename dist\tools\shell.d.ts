import type { ToolResult, ShellCommand, CommandResult } from '@/types';
export declare class ShellTool {
    private activeProcesses;
    private commandHistory;
    private maxHistorySize;
    execute(args: Record<string, unknown>): Promise<ToolResult>;
    private parseCommand;
    private parseCommandString;
    private shouldUseShell;
    private executeCommand;
    private formatOutput;
    isCommandSafe(command: string): boolean;
    getCommandRiskLevel(command: string): 'safe' | 'low' | 'medium' | 'high' | 'critical';
    killProcess(processId: string): boolean;
    killAllProcesses(): void;
    getActiveProcesses(): string[];
    private addToHistory;
    getCommandHistory(): string[];
    clearHistory(): void;
    private handleBuiltInCommands;
    executeCommandWithRetry(command: ShellCommand, maxRetries?: number): Promise<CommandResult>;
    private normalizeCommand;
    validateCommand(command: string): {
        valid: boolean;
        reason?: string;
    };
}
//# sourceMappingURL=shell.d.ts.map