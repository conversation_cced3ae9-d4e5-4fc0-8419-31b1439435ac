"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileTool = void 0;
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const glob_1 = require("glob");
const nanoid_1 = require("nanoid");
const logger_1 = require("@/utils/logger");
class FileTool {
    async read(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const filePath = args.path;
            const encoding = args.encoding || 'utf-8';
            if (!filePath) {
                throw new Error('File path is required');
            }
            const resolvedPath = path_1.default.resolve(filePath);
            // Check if file exists
            await promises_1.default.access(resolvedPath);
            // Get file stats
            const stats = await promises_1.default.stat(resolvedPath);
            if (stats.isDirectory()) {
                throw new Error('Path is a directory, not a file');
            }
            // Check file size (limit to 10MB for safety)
            if (stats.size > 10 * 1024 * 1024) {
                throw new Error('File too large (>10MB). Use file streaming for large files.');
            }
            const content = await promises_1.default.readFile(resolvedPath, encoding);
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: content,
                executionTime: Date.now() - startTime,
                metadata: {
                    path: resolvedPath,
                    size: stats.size,
                    encoding,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async write(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const filePath = args.path;
            const content = args.content;
            const encoding = args.encoding || 'utf-8';
            const backup = args.backup || false;
            if (!filePath) {
                throw new Error('File path is required');
            }
            if (content === undefined) {
                throw new Error('Content is required');
            }
            const resolvedPath = path_1.default.resolve(filePath);
            // Create backup if requested and file exists
            if (backup) {
                try {
                    await promises_1.default.access(resolvedPath);
                    const backupPath = `${resolvedPath}.backup.${Date.now()}`;
                    await promises_1.default.copyFile(resolvedPath, backupPath);
                    logger_1.logger.debug('Backup created', { original: resolvedPath, backup: backupPath });
                }
                catch (error) {
                    // File doesn't exist, no backup needed
                }
            }
            // Ensure directory exists
            const dir = path_1.default.dirname(resolvedPath);
            await promises_1.default.mkdir(dir, { recursive: true });
            // Write file
            await promises_1.default.writeFile(resolvedPath, content, encoding);
            const stats = await promises_1.default.stat(resolvedPath);
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: `File written successfully: ${resolvedPath} (${stats.size} bytes)`,
                executionTime: Date.now() - startTime,
                metadata: {
                    path: resolvedPath,
                    size: stats.size,
                    encoding,
                    backup,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async create(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const filePath = args.path;
            const content = args.content || '';
            const encoding = args.encoding || 'utf-8';
            if (!filePath) {
                throw new Error('File path is required');
            }
            const resolvedPath = path_1.default.resolve(filePath);
            // Check if file already exists
            try {
                await promises_1.default.access(resolvedPath);
                throw new Error('File already exists');
            }
            catch (error) {
                // File doesn't exist, which is what we want
                if (error.code !== 'ENOENT') {
                    throw error;
                }
            }
            // Ensure directory exists
            const dir = path_1.default.dirname(resolvedPath);
            await promises_1.default.mkdir(dir, { recursive: true });
            // Create file
            await promises_1.default.writeFile(resolvedPath, content, encoding);
            const stats = await promises_1.default.stat(resolvedPath);
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: `File created successfully: ${resolvedPath} (${stats.size} bytes)`,
                executionTime: Date.now() - startTime,
                metadata: {
                    path: resolvedPath,
                    size: stats.size,
                    encoding,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async delete(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const filePath = args.path;
            const recursive = args.recursive || false;
            const force = args.force || false;
            if (!filePath) {
                throw new Error('File path is required');
            }
            const resolvedPath = path_1.default.resolve(filePath);
            // Check if path exists
            await promises_1.default.access(resolvedPath);
            const stats = await promises_1.default.stat(resolvedPath);
            if (stats.isDirectory()) {
                if (!recursive) {
                    throw new Error('Path is a directory. Use recursive=true to delete directories.');
                }
                await promises_1.default.rm(resolvedPath, { recursive: true, force });
            }
            else {
                await promises_1.default.unlink(resolvedPath);
            }
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: `${stats.isDirectory() ? 'Directory' : 'File'} deleted successfully: ${resolvedPath}`,
                executionTime: Date.now() - startTime,
                metadata: {
                    path: resolvedPath,
                    type: stats.isDirectory() ? 'directory' : 'file',
                    recursive,
                    force,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async move(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const source = args.source;
            const destination = args.destination;
            if (!source || !destination) {
                throw new Error('Source and destination paths are required');
            }
            const resolvedSource = path_1.default.resolve(source);
            const resolvedDestination = path_1.default.resolve(destination);
            // Check if source exists
            await promises_1.default.access(resolvedSource);
            // Ensure destination directory exists
            const destDir = path_1.default.dirname(resolvedDestination);
            await promises_1.default.mkdir(destDir, { recursive: true });
            // Move file/directory
            await promises_1.default.rename(resolvedSource, resolvedDestination);
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: `Moved successfully: ${resolvedSource} → ${resolvedDestination}`,
                executionTime: Date.now() - startTime,
                metadata: {
                    source: resolvedSource,
                    destination: resolvedDestination,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async copy(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const source = args.source;
            const destination = args.destination;
            const recursive = args.recursive || false;
            if (!source || !destination) {
                throw new Error('Source and destination paths are required');
            }
            const resolvedSource = path_1.default.resolve(source);
            const resolvedDestination = path_1.default.resolve(destination);
            // Check if source exists
            await promises_1.default.access(resolvedSource);
            const stats = await promises_1.default.stat(resolvedSource);
            // Ensure destination directory exists
            const destDir = path_1.default.dirname(resolvedDestination);
            await promises_1.default.mkdir(destDir, { recursive: true });
            if (stats.isDirectory()) {
                if (!recursive) {
                    throw new Error('Source is a directory. Use recursive=true to copy directories.');
                }
                await this.copyDirectory(resolvedSource, resolvedDestination);
            }
            else {
                await promises_1.default.copyFile(resolvedSource, resolvedDestination);
            }
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: `Copied successfully: ${resolvedSource} → ${resolvedDestination}`,
                executionTime: Date.now() - startTime,
                metadata: {
                    source: resolvedSource,
                    destination: resolvedDestination,
                    type: stats.isDirectory() ? 'directory' : 'file',
                    recursive,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async search(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const pattern = args.pattern;
            const cwd = args.cwd || process.cwd();
            const maxDepth = args.maxDepth || 10;
            if (!pattern) {
                throw new Error('Search pattern is required');
            }
            const files = await (0, glob_1.glob)(pattern, {
                cwd: path_1.default.resolve(cwd),
                maxDepth,
                dot: false,
            });
            const results = files.slice(0, 100); // Limit results for performance
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: results.length > 0
                    ? `Found ${results.length} files:\n${results.join('\n')}`
                    : 'No files found matching the pattern',
                executionTime: Date.now() - startTime,
                metadata: {
                    pattern,
                    cwd,
                    maxDepth,
                    resultCount: results.length,
                    results,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async grep(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const pattern = args.pattern;
            const files = args.files;
            const recursive = args.recursive || false;
            const ignoreCase = args.ignoreCase || false;
            const lineNumbers = args.lineNumbers || true;
            if (!pattern) {
                throw new Error('Search pattern is required');
            }
            if (!files || files.length === 0) {
                throw new Error('Files to search are required');
            }
            const regex = new RegExp(pattern, ignoreCase ? 'gi' : 'g');
            const results = [];
            for (const file of files) {
                try {
                    const resolvedPath = path_1.default.resolve(file);
                    const stats = await promises_1.default.stat(resolvedPath);
                    if (stats.isDirectory() && recursive) {
                        // Search in directory recursively
                        const dirFiles = await (0, glob_1.glob)('**/*', {
                            cwd: resolvedPath,
                            nodir: true,
                            maxDepth: 5,
                        });
                        for (const dirFile of dirFiles) {
                            const fullPath = path_1.default.join(resolvedPath, dirFile);
                            const matches = await this.searchInFile(fullPath, regex, lineNumbers);
                            if (matches.length > 0) {
                                results.push(`${fullPath}:\n${matches.join('\n')}`);
                            }
                        }
                    }
                    else if (stats.isFile()) {
                        const matches = await this.searchInFile(resolvedPath, regex, lineNumbers);
                        if (matches.length > 0) {
                            results.push(`${resolvedPath}:\n${matches.join('\n')}`);
                        }
                    }
                }
                catch (error) {
                    // Skip files that can't be read
                    logger_1.logger.debug('Skipping file in grep', { file, error });
                }
            }
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: results.length > 0
                    ? results.join('\n\n')
                    : 'No matches found',
                executionTime: Date.now() - startTime,
                metadata: {
                    pattern,
                    files,
                    recursive,
                    ignoreCase,
                    lineNumbers,
                    matchCount: results.length,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async copyDirectory(source, destination) {
        await promises_1.default.mkdir(destination, { recursive: true });
        const entries = await promises_1.default.readdir(source, { withFileTypes: true });
        for (const entry of entries) {
            const srcPath = path_1.default.join(source, entry.name);
            const destPath = path_1.default.join(destination, entry.name);
            if (entry.isDirectory()) {
                await this.copyDirectory(srcPath, destPath);
            }
            else {
                await promises_1.default.copyFile(srcPath, destPath);
            }
        }
    }
    async searchInFile(filePath, regex, lineNumbers) {
        try {
            const content = await promises_1.default.readFile(filePath, 'utf-8');
            const lines = content.split('\n');
            const matches = [];
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                if (regex.test(line)) {
                    const match = lineNumbers
                        ? `${i + 1}: ${line}`
                        : line;
                    matches.push(match);
                }
            }
            return matches;
        }
        catch (error) {
            return [];
        }
    }
}
exports.FileTool = FileTool;
//# sourceMappingURL=files.js.map