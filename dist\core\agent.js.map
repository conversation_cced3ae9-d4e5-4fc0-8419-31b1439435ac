{"version": 3, "file": "agent.js", "sourceRoot": "", "sources": ["../../src/core/agent.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AACtC,mCAAgC;AAChC,2CAAwC;AACxC,uCAA2C;AAC3C,uCAA2C;AAC3C,8CAAmD;AACnD,+CAAgD;AAChD,kDAAqD;AACrD,oDAAqD;AAYrD,MAAa,KAAM,SAAQ,qBAAY;IAUjB;IATZ,cAAc,CAAiB;IAC/B,cAAc,CAAiB;IAC/B,UAAU,CAAoB;IAC9B,YAAY,CAAe;IAC3B,cAAc,CAAiB;IAC/B,aAAa,CAAgB;IAC7B,cAAc,GAAmB,IAAI,CAAC;IACtC,YAAY,GAAG,KAAK,CAAC;IAE7B,YAAoB,MAAmB;QACrC,KAAK,EAAE,CAAC;QADU,WAAM,GAAN,MAAM,CAAa;QAGrC,IAAI,CAAC,cAAc,GAAG,IAAI,wBAAc,EAAE,CAAC;QAC3C,IAAI,CAAC,cAAc,GAAG,IAAI,wBAAc,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QAClE,IAAI,CAAC,UAAU,GAAG,IAAI,6BAAiB,CAAC,MAAM,CAAC,CAAC;QAChD,IAAI,CAAC,YAAY,GAAG,IAAI,uBAAY,EAAE,CAAC;QACvC,IAAI,CAAC,cAAc,GAAG,IAAI,yBAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAC9D,IAAI,CAAC,aAAa,GAAG,IAAI,yBAAa,EAAE,CAAC;QAEzC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,eAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC/B,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,YAAY,EAAE,MAAM,CAAC,YAAY;SAClC,CAAC,CAAC;IACL,CAAC;IAEO,kBAAkB;QACxB,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,OAAO,EAAE,EAAE;YACpD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,QAAQ,EAAE,EAAE;YACvD,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,EAAE;YAC/C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;YACvC,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;YACvC,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;YAErC,2BAA2B;YAC3B,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBAC1B,IAAI,CAAC,cAAc,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACrF,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,cAAc,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;oBAC5D,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,gBAAgB;oBAC9C,OAAO,EAAE,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE;iBAChD,CAAC,CAAC;YACL,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;gBAC5C,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,EAAE;aAClC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,OAAe,EACf,aAA6B;QAE7B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,MAAM,SAAS,GAAG,IAAA,eAAM,GAAE,CAAC;QAE3B,IAAI,CAAC;YACH,mCAAmC;YACnC,MAAM,WAAW,GAAwB;gBACvC,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;YAC1E,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;YAExC,sBAAsB;YACtB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;YAEvD,+BAA+B;YAC/B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;YAE/E,sBAAsB;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC;YAEpD,qBAAqB;YACrB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC;gBAC9D,QAAQ;gBACR,KAAK;gBACL,OAAO;gBACP,OAAO,EAAE,aAAa,EAAE,OAAO;gBAC/B,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;oBAC7B,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBAC7C,CAAC;aACF,CAAC,CAAC;YAEH,wCAAwC;YACxC,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC;YAC/E,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAC;YAE7C,2BAA2B;YAC3B,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;YAE1C,OAAO,gBAAgB,CAAC;QAE1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAChD,aAAa,EAAE,OAAO,CAAC,KAAc,CAAC,CAAC;YACvC,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,aAAa,EAAE,UAAU,EAAE,CAAC;QAC9B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,QAAkB;QAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,2BAA2B;YAC3B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACrE,QAAQ,CAAC,SAAS,GAAG,cAAc,CAAC,KAAK,CAAC;YAE1C,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC,CAAC;YAE9D,gCAAgC;YAChC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;YAErF,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO;oBACL,EAAE,EAAE,IAAA,eAAM,GAAE;oBACZ,UAAU,EAAE,QAAQ,CAAC,EAAE;oBACvB,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,EAAE;oBACV,KAAK,EAAE,4BAA4B;oBACnC,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;iBACtC,CAAC;YACJ,CAAC;YAED,mBAAmB;YACnB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAE7D,kCAAkC;YAClC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YAC7E,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YACnC,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAE5C,MAAM,WAAW,GAAe;gBAC9B,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACvB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;gBAC/D,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACtC,CAAC;YAEF,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC,CAAC;YAC1D,OAAO,WAAW,CAAC;QACrB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,WAAW;QACf,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,SAAiB;QACnC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QACjE,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;QAE9B,wCAAwC;QACxC,IAAI,OAAO,CAAC,gBAAgB,KAAK,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAC9D,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;YACxD,MAAM,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;YACtD,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,gBAAgB;YAC9C,OAAO,EAAE,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE;SAChD,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;QAC9B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;QACtC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,SAAiB;QACnC,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAEnD,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,KAAK,SAAS,EAAE,CAAC;YAC1C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,SAA+B;QAChD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAEtC,IAAI,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,MAAM,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACnF,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,SAAS,CAAC,YAAY,EAAE,CAAC;YAC3B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,SAAS,CAAC,gBAAgB,EAAE,CAAC;YAC/B,MAAM,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,UAAU;QACd,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC7D,CAAC;YAED,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YAEnC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,eAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,yBAAyB;IACzB,OAAO,CAA8B,KAAa,EAAE,OAAwB;QAC1E,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAC1B,CAAC;IAED,QAAQ,CAA8B,KAAa,EAAE,OAAwB;QAC3E,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAC3B,CAAC;IAED,kBAAkB;IAClB,OAAO;QACL,OAAO,IAAI,CAAC,cAAc,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;IAC5D,CAAC;IAED,SAAS;QACP,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE;YACrB,UAAU,EAAE,IAAI,CAAC,YAAY;YAC7B,SAAS,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE;YAClC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC9B,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;YACxB,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;SACvC,CAAC;IACJ,CAAC;CACF;AAtSD,sBAsSC"}