"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShellTool = void 0;
const cross_spawn_1 = require("cross-spawn");
const nanoid_1 = require("nanoid");
const logger_1 = require("@/utils/logger");
const os_1 = __importDefault(require("os"));
class ShellTool {
    activeProcesses = new Map();
    commandHistory = [];
    maxHistorySize = 1000;
    async execute(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const command = this.parseCommand(args);
            // Add to command history
            this.addToHistory(command.command + ' ' + command.args.join(' '));
            // Check if it's a built-in shell command
            const builtInResult = await this.handleBuiltInCommands(command);
            if (builtInResult) {
                return {
                    id: resultId,
                    toolCallId: args.toolCallId || '',
                    success: builtInResult.success,
                    output: builtInResult.output,
                    error: builtInResult.error,
                    executionTime: Date.now() - startTime,
                    metadata: {
                        command: command.command,
                        builtIn: true,
                    },
                };
            }
            const result = await this.executeCommand(command);
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: result.success,
                output: this.formatOutput(result),
                error: result.success ? undefined : result.stderr,
                executionTime: Date.now() - startTime,
                metadata: {
                    command: result.command,
                    exitCode: result.exitCode,
                    executionTime: result.executionTime,
                },
            };
        }
        catch (error) {
            logger_1.logger.error('Shell command execution failed', error);
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    parseCommand(args) {
        const command = args.command;
        const commandArgs = args.args || [];
        const cwd = args.cwd;
        const timeout = args.timeout || 30000;
        const env = args.env;
        if (!command) {
            throw new Error('Command is required');
        }
        // Parse command if it's a string with arguments
        let finalCommand = command;
        let finalArgs = commandArgs;
        if (commandArgs.length === 0 && command.includes(' ')) {
            const parts = this.parseCommandString(command);
            finalCommand = parts[0];
            finalArgs = parts.slice(1);
        }
        return {
            command: finalCommand,
            args: finalArgs,
            cwd: cwd || process.cwd(),
            env: env ? { ...process.env, ...env } : process.env,
            timeout,
            shell: this.shouldUseShell(finalCommand),
        };
    }
    parseCommandString(commandString) {
        const parts = [];
        let current = '';
        let inQuotes = false;
        let quoteChar = '';
        for (let i = 0; i < commandString.length; i++) {
            const char = commandString[i];
            if ((char === '"' || char === "'") && !inQuotes) {
                inQuotes = true;
                quoteChar = char;
            }
            else if (char === quoteChar && inQuotes) {
                inQuotes = false;
                quoteChar = '';
            }
            else if (char === ' ' && !inQuotes) {
                if (current) {
                    parts.push(current);
                    current = '';
                }
            }
            else {
                current += char;
            }
        }
        if (current) {
            parts.push(current);
        }
        return parts;
    }
    shouldUseShell(command) {
        // Commands that typically require shell
        const shellCommands = [
            'cd', 'pwd', 'echo', 'set', 'export', 'source', '.',
            'alias', 'unalias', 'history', 'jobs', 'fg', 'bg',
        ];
        // Check if command contains shell operators
        const shellOperators = ['|', '>', '<', '>>', '<<', '&&', '||', ';', '&'];
        return shellCommands.includes(command) ||
            shellOperators.some(op => command.includes(op));
    }
    async executeCommand(command) {
        const startTime = Date.now();
        return new Promise((resolve) => {
            const processId = (0, nanoid_1.nanoid)();
            let stdout = '';
            let stderr = '';
            logger_1.logger.debug('Executing command', {
                command: command.command,
                args: command.args,
                cwd: command.cwd
            });
            const child = (0, cross_spawn_1.spawn)(command.command, command.args, {
                cwd: command.cwd,
                env: command.env,
                shell: command.shell,
                stdio: 'pipe',
            });
            this.activeProcesses.set(processId, child);
            // Set up timeout
            const timeout = setTimeout(() => {
                child.kill('SIGTERM');
                setTimeout(() => {
                    if (!child.killed) {
                        child.kill('SIGKILL');
                    }
                }, 5000);
            }, command.timeout);
            // Collect stdout
            if (child.stdout) {
                child.stdout.on('data', (data) => {
                    stdout += data.toString();
                });
            }
            // Collect stderr
            if (child.stderr) {
                child.stderr.on('data', (data) => {
                    stderr += data.toString();
                });
            }
            // Handle process completion
            child.on('close', (code, signal) => {
                clearTimeout(timeout);
                this.activeProcesses.delete(processId);
                const executionTime = Date.now() - startTime;
                const success = code === 0;
                const result = {
                    success,
                    exitCode: code || 0,
                    stdout: stdout.trim(),
                    stderr: stderr.trim(),
                    executionTime,
                    command: `${command.command} ${command.args.join(' ')}`.trim(),
                };
                if (signal) {
                    result.stderr += `\nProcess terminated by signal: ${signal}`;
                }
                logger_1.logger.debug('Command completed', {
                    command: result.command,
                    success,
                    exitCode: code,
                    executionTime
                });
                resolve(result);
            });
            // Handle process errors
            child.on('error', (error) => {
                clearTimeout(timeout);
                this.activeProcesses.delete(processId);
                const result = {
                    success: false,
                    exitCode: 1,
                    stdout: '',
                    stderr: error.message,
                    executionTime: Date.now() - startTime,
                    command: `${command.command} ${command.args.join(' ')}`.trim(),
                };
                logger_1.logger.error('Command failed', {
                    command: result.command,
                    error: error.message
                });
                resolve(result);
            });
        });
    }
    formatOutput(result) {
        let output = '';
        if (result.stdout) {
            output += result.stdout;
        }
        if (result.stderr && !result.success) {
            if (output)
                output += '\n\n';
            output += `Error: ${result.stderr}`;
        }
        if (!output && result.success) {
            output = 'Command executed successfully (no output)';
        }
        // Add execution metadata
        output += `\n\n[Execution completed in ${result.executionTime}ms with exit code ${result.exitCode}]`;
        return output;
    }
    // Utility methods for command validation and safety
    isCommandSafe(command) {
        const dangerousCommands = [
            'rm -rf /',
            'rm -rf *',
            'dd if=',
            'mkfs',
            'fdisk',
            'format',
            'del /s',
            'rmdir /s',
            'shutdown',
            'reboot',
            'halt',
            'poweroff',
        ];
        const lowerCommand = command.toLowerCase();
        return !dangerousCommands.some(dangerous => lowerCommand.includes(dangerous.toLowerCase()));
    }
    getCommandRiskLevel(command) {
        const lowerCommand = command.toLowerCase();
        // Critical risk commands
        if (lowerCommand.includes('rm -rf') ||
            lowerCommand.includes('format') ||
            lowerCommand.includes('fdisk') ||
            lowerCommand.includes('mkfs')) {
            return 'critical';
        }
        // High risk commands
        if (lowerCommand.includes('sudo') ||
            lowerCommand.includes('su ') ||
            lowerCommand.includes('chmod 777') ||
            lowerCommand.includes('chown') ||
            lowerCommand.startsWith('rm ') ||
            lowerCommand.includes('del ')) {
            return 'high';
        }
        // Medium risk commands
        if (lowerCommand.includes('install') ||
            lowerCommand.includes('uninstall') ||
            lowerCommand.includes('update') ||
            lowerCommand.includes('upgrade') ||
            lowerCommand.includes('git push') ||
            lowerCommand.includes('npm publish')) {
            return 'medium';
        }
        // Low risk commands
        if (lowerCommand.includes('mkdir') ||
            lowerCommand.includes('touch') ||
            lowerCommand.includes('cp ') ||
            lowerCommand.includes('mv ') ||
            lowerCommand.includes('git add') ||
            lowerCommand.includes('git commit')) {
            return 'low';
        }
        // Safe commands (read-only operations)
        return 'safe';
    }
    killProcess(processId) {
        const process = this.activeProcesses.get(processId);
        if (process) {
            process.kill('SIGTERM');
            this.activeProcesses.delete(processId);
            return true;
        }
        return false;
    }
    killAllProcesses() {
        for (const [id, process] of this.activeProcesses) {
            process.kill('SIGTERM');
            this.activeProcesses.delete(id);
        }
    }
    getActiveProcesses() {
        return Array.from(this.activeProcesses.keys());
    }
    // Command history management
    addToHistory(command) {
        this.commandHistory.push(command);
        if (this.commandHistory.length > this.maxHistorySize) {
            this.commandHistory.shift();
        }
    }
    getCommandHistory() {
        return [...this.commandHistory];
    }
    clearHistory() {
        this.commandHistory = [];
    }
    // Built-in command handlers
    async handleBuiltInCommands(command) {
        const cmd = command.command.toLowerCase();
        switch (cmd) {
            case 'pwd':
                return {
                    success: true,
                    output: command.cwd || process.cwd(),
                };
            case 'whoami':
                return {
                    success: true,
                    output: os_1.default.userInfo().username,
                };
            case 'history':
                return {
                    success: true,
                    output: this.commandHistory.map((cmd, i) => `${i + 1}  ${cmd}`).join('\n'),
                };
            case 'echo':
                return {
                    success: true,
                    output: command.args.join(' '),
                };
            case 'env':
                if (command.args.length === 0) {
                    const envVars = Object.entries(command.env || process.env)
                        .map(([key, value]) => `${key}=${value}`)
                        .sort()
                        .join('\n');
                    return {
                        success: true,
                        output: envVars,
                    };
                }
                break;
            case 'which':
                if (command.args.length > 0) {
                    const commandName = command.args[0];
                    const result = (0, cross_spawn_1.spawnSync)('which', [commandName], { encoding: 'utf8' });
                    return {
                        success: result.status === 0,
                        output: result.stdout.trim(),
                        error: result.stderr.trim() || undefined,
                    };
                }
                break;
        }
        return null;
    }
    // Enhanced command execution with better error handling
    async executeCommandWithRetry(command, maxRetries = 3) {
        let lastError = null;
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const result = await this.executeCommand(command);
                if (result.success || attempt === maxRetries) {
                    return result;
                }
                lastError = new Error(result.stderr);
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));
                if (attempt === maxRetries) {
                    throw lastError;
                }
            }
            // Wait before retry (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
        throw lastError || new Error('Command execution failed after retries');
    }
    // Platform-specific command normalization
    normalizeCommand(command) {
        const platform = os_1.default.platform();
        // Windows command mappings
        if (platform === 'win32') {
            const windowsCommands = {
                'ls': 'dir',
                'cat': 'type',
                'grep': 'findstr',
                'ps': 'tasklist',
                'kill': 'taskkill',
                'which': 'where',
                'touch': 'echo. >',
                'rm': 'del',
                'mv': 'move',
                'cp': 'copy',
            };
            return windowsCommands[command] || command;
        }
        return command;
    }
    // Command validation and sanitization
    validateCommand(command) {
        if (!command || command.trim().length === 0) {
            return { valid: false, reason: 'Command cannot be empty' };
        }
        // Check for dangerous patterns
        const dangerousPatterns = [
            /rm\s+-rf\s+\/$/,
            /rm\s+-rf\s+\*$/,
            /dd\s+if=/,
            /mkfs/,
            /fdisk/,
            /format\s+c:/i,
            /del\s+\/s/i,
            /rmdir\s+\/s/i,
        ];
        for (const pattern of dangerousPatterns) {
            if (pattern.test(command)) {
                return { valid: false, reason: 'Command contains dangerous patterns' };
            }
        }
        return { valid: true };
    }
}
exports.ShellTool = ShellTool;
//# sourceMappingURL=shell.js.map