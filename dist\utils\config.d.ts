import { z } from 'zod';
import type { Agent<PERSON>onfig, AIProvider } from '@/types';
declare const ConfigSchema: z.ZodObject<{
    provider: z.ZodDefault<z.ZodEnum<["openai", "deepseek", "ollama", "azure"]>>;
    model: z.<PERSON><PERSON><PERSON><z.ZodString>;
    apiKey: z.Zod<PERSON>ptional<z.ZodString>;
    baseUrl: z.ZodOptional<z.ZodString>;
    maxTokens: z.ZodDefault<z.ZodNumber>;
    temperature: z.ZodDefault<z.ZodNumber>;
    approvalMode: z.ZodDefault<z.ZodEnum<["suggest", "auto-edit", "full-auto"]>>;
    workingDirectory: z.<PERSON>od<PERSON>efault<z.ZodString>;
    security: z.ZodOptional<z.ZodObject<{
        allowedCommands: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        blockedCommands: z.ZodOptional<z.<PERSON><z.ZodString, "many">>;
        allowedPaths: z.<PERSON><z.<PERSON>od<PERSON>y<z.ZodString, "many">>;
        blockedPaths: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        maxExecutionTime: z.ZodDefault<z.ZodNumber>;
        sandboxMode: z.ZodDefault<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        maxExecutionTime: number;
        sandboxMode: boolean;
        allowedCommands?: string[] | undefined;
        blockedCommands?: string[] | undefined;
        allowedPaths?: string[] | undefined;
        blockedPaths?: string[] | undefined;
    }, {
        allowedCommands?: string[] | undefined;
        blockedCommands?: string[] | undefined;
        maxExecutionTime?: number | undefined;
        sandboxMode?: boolean | undefined;
        allowedPaths?: string[] | undefined;
        blockedPaths?: string[] | undefined;
    }>>;
    logging: z.ZodOptional<z.ZodObject<{
        level: z.ZodDefault<z.ZodEnum<["error", "warn", "info", "debug"]>>;
        console: z.ZodDefault<z.ZodBoolean>;
        file: z.ZodDefault<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        level: "info" | "error" | "debug" | "warn";
        file: boolean;
        console: boolean;
    }, {
        level?: "info" | "error" | "debug" | "warn" | undefined;
        file?: boolean | undefined;
        console?: boolean | undefined;
    }>>;
    ui: z.ZodOptional<z.ZodObject<{
        interactive: z.ZodDefault<z.ZodBoolean>;
        verbose: z.ZodDefault<z.ZodBoolean>;
        quiet: z.ZodDefault<z.ZodBoolean>;
        theme: z.ZodDefault<z.ZodEnum<["default", "dark", "light"]>>;
    }, "strip", z.ZodTypeAny, {
        interactive: boolean;
        verbose: boolean;
        quiet: boolean;
        theme: "default" | "dark" | "light";
    }, {
        interactive?: boolean | undefined;
        verbose?: boolean | undefined;
        quiet?: boolean | undefined;
        theme?: "default" | "dark" | "light" | undefined;
    }>>;
    session: z.ZodOptional<z.ZodObject<{
        autoSave: z.ZodDefault<z.ZodBoolean>;
        maxSessions: z.ZodDefault<z.ZodNumber>;
        cleanupDays: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        autoSave: boolean;
        maxSessions: number;
        cleanupDays: number;
    }, {
        autoSave?: boolean | undefined;
        maxSessions?: number | undefined;
        cleanupDays?: number | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    provider: "openai" | "deepseek" | "ollama" | "azure";
    model: string;
    maxTokens: number;
    temperature: number;
    approvalMode: "suggest" | "auto-edit" | "full-auto";
    workingDirectory: string;
    session?: {
        autoSave: boolean;
        maxSessions: number;
        cleanupDays: number;
    } | undefined;
    security?: {
        maxExecutionTime: number;
        sandboxMode: boolean;
        allowedCommands?: string[] | undefined;
        blockedCommands?: string[] | undefined;
        allowedPaths?: string[] | undefined;
        blockedPaths?: string[] | undefined;
    } | undefined;
    apiKey?: string | undefined;
    baseUrl?: string | undefined;
    logging?: {
        level: "info" | "error" | "debug" | "warn";
        file: boolean;
        console: boolean;
    } | undefined;
    ui?: {
        interactive: boolean;
        verbose: boolean;
        quiet: boolean;
        theme: "default" | "dark" | "light";
    } | undefined;
}, {
    session?: {
        autoSave?: boolean | undefined;
        maxSessions?: number | undefined;
        cleanupDays?: number | undefined;
    } | undefined;
    security?: {
        allowedCommands?: string[] | undefined;
        blockedCommands?: string[] | undefined;
        maxExecutionTime?: number | undefined;
        sandboxMode?: boolean | undefined;
        allowedPaths?: string[] | undefined;
        blockedPaths?: string[] | undefined;
    } | undefined;
    provider?: "openai" | "deepseek" | "ollama" | "azure" | undefined;
    model?: string | undefined;
    apiKey?: string | undefined;
    baseUrl?: string | undefined;
    maxTokens?: number | undefined;
    temperature?: number | undefined;
    approvalMode?: "suggest" | "auto-edit" | "full-auto" | undefined;
    workingDirectory?: string | undefined;
    logging?: {
        level?: "info" | "error" | "debug" | "warn" | undefined;
        file?: boolean | undefined;
        console?: boolean | undefined;
    } | undefined;
    ui?: {
        interactive?: boolean | undefined;
        verbose?: boolean | undefined;
        quiet?: boolean | undefined;
        theme?: "default" | "dark" | "light" | undefined;
    } | undefined;
}>;
type Config = z.infer<typeof ConfigSchema>;
export declare class ConfigManager {
    private configPath;
    private config;
    constructor(configPath?: string);
    private getDefaultConfigPath;
    load(): Promise<Config>;
    private createDefaultConfig;
    private applyEnvironmentOverrides;
    save(): Promise<void>;
    update(updates: Partial<Config>): Promise<void>;
    get(): Config;
    toAgentConfig(): AgentConfig;
    getProviderConfig(): {
        provider: "openai" | "deepseek" | "ollama" | "azure";
        model: string;
        apiKey: string | undefined;
        baseUrl: string | undefined;
    } | null;
    getSecurityConfig(): {};
    getLoggingConfig(): {};
    getUIConfig(): {};
    getSessionConfig(): {};
    validateConfig(): Promise<{
        valid: boolean;
        errors: string[];
    }>;
    static getProviderDefaults(provider: AIProvider): Partial<Config>;
    static createTemplate(provider: AIProvider): Config;
    exportConfig(): Promise<string>;
    importConfig(configYaml: string): Promise<void>;
}
export declare const configManager: ConfigManager;
export declare function loadConfig(configPath?: string): Promise<AgentConfig>;
export {};
//# sourceMappingURL=config.d.ts.map