import { spawn } from 'cross-spawn';
import type { ChildProcess } from 'child_process';
import { nanoid } from 'nanoid';
import { logger } from '@/utils/logger';
import type { ToolResult, ShellCommand, CommandResult } from '@/types';
import os from 'os';

export class ShellTool {
  private activeProcesses: Map<string, ChildProcess> = new Map();
  private commandHistory: string[] = [];
  private maxHistorySize = 1000;

  async execute(args: Record<string, unknown>): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const command = this.parseCommand(args);

      // Add to command history
      this.addToHistory(command.command + ' ' + command.args.join(' '));

      // Check if it's a built-in shell command
      const builtInResult = await this.handleBuiltInCommands(command);
      if (builtInResult) {
        return {
          id: resultId,
          toolCallId: args.toolCallId as string || '',
          success: builtInResult.success,
          output: builtInResult.output,
          error: builtInResult.error,
          executionTime: Date.now() - startTime,
          metadata: {
            command: command.command,
            builtIn: true,
          },
        };
      }

      const result = await this.executeCommand(command);

      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: result.success,
        output: this.formatOutput(result),
        error: result.success ? undefined : result.stderr,
        executionTime: Date.now() - startTime,
        metadata: {
          command: result.command,
          exitCode: result.exitCode,
          executionTime: result.executionTime,
        },
      };
    } catch (error) {
      logger.error('Shell command execution failed', error);

      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  private parseCommand(args: Record<string, unknown>): ShellCommand {
    const command = args.command as string;
    const commandArgs = (args.args as string[]) || [];
    const cwd = args.cwd as string;
    const timeout = (args.timeout as number) || 30000;
    const env = args.env as Record<string, string>;

    if (!command) {
      throw new Error('Command is required');
    }

    // Parse command if it's a string with arguments
    let finalCommand = command;
    let finalArgs = commandArgs;

    if (commandArgs.length === 0 && command.includes(' ')) {
      const parts = this.parseCommandString(command);
      finalCommand = parts[0];
      finalArgs = parts.slice(1);
    }

    return {
      command: finalCommand,
      args: finalArgs,
      cwd: cwd || process.cwd(),
      env: env ? { ...process.env, ...env } : process.env as Record<string, string>,
      timeout,
      shell: this.shouldUseShell(finalCommand),
    };
  }

  private parseCommandString(commandString: string): string[] {
    const parts: string[] = [];
    let current = '';
    let inQuotes = false;
    let quoteChar = '';

    for (let i = 0; i < commandString.length; i++) {
      const char = commandString[i];

      if ((char === '"' || char === "'") && !inQuotes) {
        inQuotes = true;
        quoteChar = char;
      } else if (char === quoteChar && inQuotes) {
        inQuotes = false;
        quoteChar = '';
      } else if (char === ' ' && !inQuotes) {
        if (current) {
          parts.push(current);
          current = '';
        }
      } else {
        current += char;
      }
    }

    if (current) {
      parts.push(current);
    }

    return parts;
  }

  private shouldUseShell(command: string): boolean {
    // Commands that typically require shell
    const shellCommands = [
      'cd', 'pwd', 'echo', 'set', 'export', 'source', '.',
      'alias', 'unalias', 'history', 'jobs', 'fg', 'bg',
    ];

    // Check if command contains shell operators
    const shellOperators = ['|', '>', '<', '>>', '<<', '&&', '||', ';', '&'];
    
    return shellCommands.includes(command) || 
           shellOperators.some(op => command.includes(op));
  }

  private async executeCommand(command: ShellCommand): Promise<CommandResult> {
    const startTime = Date.now();

    return new Promise((resolve) => {
      const processId = nanoid();
      let stdout = '';
      let stderr = '';

      logger.debug('Executing command', { 
        command: command.command, 
        args: command.args,
        cwd: command.cwd 
      });

      const child = spawn(command.command, command.args, {
        cwd: command.cwd,
        env: command.env,
        shell: command.shell,
        stdio: 'pipe',
      });

      this.activeProcesses.set(processId, child);

      // Set up timeout
      const timeout = setTimeout(() => {
        child.kill('SIGTERM');
        setTimeout(() => {
          if (!child.killed) {
            child.kill('SIGKILL');
          }
        }, 5000);
      }, command.timeout);

      // Collect stdout
      if (child.stdout) {
        child.stdout.on('data', (data) => {
          stdout += data.toString();
        });
      }

      // Collect stderr
      if (child.stderr) {
        child.stderr.on('data', (data) => {
          stderr += data.toString();
        });
      }

      // Handle process completion
      child.on('close', (code, signal) => {
        clearTimeout(timeout);
        this.activeProcesses.delete(processId);

        const executionTime = Date.now() - startTime;
        const success = code === 0;

        const result: CommandResult = {
          success,
          exitCode: code || 0,
          stdout: stdout.trim(),
          stderr: stderr.trim(),
          executionTime,
          command: `${command.command} ${command.args.join(' ')}`.trim(),
        };

        if (signal) {
          result.stderr += `\nProcess terminated by signal: ${signal}`;
        }

        logger.debug('Command completed', { 
          command: result.command,
          success,
          exitCode: code,
          executionTime 
        });

        resolve(result);
      });

      // Handle process errors
      child.on('error', (error) => {
        clearTimeout(timeout);
        this.activeProcesses.delete(processId);

        const result: CommandResult = {
          success: false,
          exitCode: 1,
          stdout: '',
          stderr: error.message,
          executionTime: Date.now() - startTime,
          command: `${command.command} ${command.args.join(' ')}`.trim(),
        };

        logger.error('Command failed', { 
          command: result.command,
          error: error.message 
        });

        resolve(result);
      });
    });
  }

  private formatOutput(result: CommandResult): string {
    let output = '';

    if (result.stdout) {
      output += result.stdout;
    }

    if (result.stderr && !result.success) {
      if (output) output += '\n\n';
      output += `Error: ${result.stderr}`;
    }

    if (!output && result.success) {
      output = 'Command executed successfully (no output)';
    }

    // Add execution metadata
    output += `\n\n[Execution completed in ${result.executionTime}ms with exit code ${result.exitCode}]`;

    return output;
  }

  // Utility methods for command validation and safety
  isCommandSafe(command: string): boolean {
    const dangerousCommands = [
      'rm -rf /',
      'rm -rf *',
      'dd if=',
      'mkfs',
      'fdisk',
      'format',
      'del /s',
      'rmdir /s',
      'shutdown',
      'reboot',
      'halt',
      'poweroff',
    ];

    const lowerCommand = command.toLowerCase();
    return !dangerousCommands.some(dangerous => 
      lowerCommand.includes(dangerous.toLowerCase())
    );
  }

  getCommandRiskLevel(command: string): 'safe' | 'low' | 'medium' | 'high' | 'critical' {
    const lowerCommand = command.toLowerCase();

    // Critical risk commands
    if (lowerCommand.includes('rm -rf') || 
        lowerCommand.includes('format') ||
        lowerCommand.includes('fdisk') ||
        lowerCommand.includes('mkfs')) {
      return 'critical';
    }

    // High risk commands
    if (lowerCommand.includes('sudo') ||
        lowerCommand.includes('su ') ||
        lowerCommand.includes('chmod 777') ||
        lowerCommand.includes('chown') ||
        lowerCommand.startsWith('rm ') ||
        lowerCommand.includes('del ')) {
      return 'high';
    }

    // Medium risk commands
    if (lowerCommand.includes('install') ||
        lowerCommand.includes('uninstall') ||
        lowerCommand.includes('update') ||
        lowerCommand.includes('upgrade') ||
        lowerCommand.includes('git push') ||
        lowerCommand.includes('npm publish')) {
      return 'medium';
    }

    // Low risk commands
    if (lowerCommand.includes('mkdir') ||
        lowerCommand.includes('touch') ||
        lowerCommand.includes('cp ') ||
        lowerCommand.includes('mv ') ||
        lowerCommand.includes('git add') ||
        lowerCommand.includes('git commit')) {
      return 'low';
    }

    // Safe commands (read-only operations)
    return 'safe';
  }

  killProcess(processId: string): boolean {
    const process = this.activeProcesses.get(processId);
    if (process) {
      process.kill('SIGTERM');
      this.activeProcesses.delete(processId);
      return true;
    }
    return false;
  }

  killAllProcesses(): void {
    for (const [id, process] of this.activeProcesses) {
      process.kill('SIGTERM');
      this.activeProcesses.delete(id);
    }
  }

  getActiveProcesses(): string[] {
    return Array.from(this.activeProcesses.keys());
  }

  // Command history management
  private addToHistory(command: string): void {
    this.commandHistory.push(command);
    if (this.commandHistory.length > this.maxHistorySize) {
      this.commandHistory.shift();
    }
  }

  getCommandHistory(): string[] {
    return [...this.commandHistory];
  }

  clearHistory(): void {
    this.commandHistory = [];
  }

  // Built-in command handlers
  private async handleBuiltInCommands(command: ShellCommand): Promise<{ success: boolean; output: string; error?: string } | null> {
    const cmd = command.command.toLowerCase();

    switch (cmd) {
      case 'pwd':
        return {
          success: true,
          output: command.cwd || process.cwd(),
        };

      case 'whoami':
        return {
          success: true,
          output: os.userInfo().username,
        };

      case 'history':
        return {
          success: true,
          output: this.commandHistory.map((cmd, i) => `${i + 1}  ${cmd}`).join('\n'),
        };

      case 'echo':
        return {
          success: true,
          output: command.args.join(' '),
        };

      case 'env':
        if (command.args.length === 0) {
          const envVars = Object.entries(command.env || process.env)
            .map(([key, value]) => `${key}=${value}`)
            .sort()
            .join('\n');
          return {
            success: true,
            output: envVars,
          };
        }
        break;

      case 'which':
        if (command.args.length > 0) {
          const commandName = command.args[0];
          const result = spawnSync('which', [commandName], { encoding: 'utf8' });
          return {
            success: result.status === 0,
            output: result.stdout.trim(),
            error: result.stderr.trim() || undefined,
          };
        }
        break;
    }

    return null;
  }

  // Enhanced command execution with better error handling
  async executeCommandWithRetry(command: ShellCommand, maxRetries = 3): Promise<CommandResult> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = await this.executeCommand(command);
        if (result.success || attempt === maxRetries) {
          return result;
        }
        lastError = new Error(result.stderr);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        if (attempt === maxRetries) {
          throw lastError;
        }
      }

      // Wait before retry (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }

    throw lastError || new Error('Command execution failed after retries');
  }

  // Platform-specific command normalization
  private normalizeCommand(command: string): string {
    const platform = os.platform();

    // Windows command mappings
    if (platform === 'win32') {
      const windowsCommands: Record<string, string> = {
        'ls': 'dir',
        'cat': 'type',
        'grep': 'findstr',
        'ps': 'tasklist',
        'kill': 'taskkill',
        'which': 'where',
        'touch': 'echo. >',
        'rm': 'del',
        'mv': 'move',
        'cp': 'copy',
      };

      return windowsCommands[command] || command;
    }

    return command;
  }

  // Command validation and sanitization
  validateCommand(command: string): { valid: boolean; reason?: string } {
    if (!command || command.trim().length === 0) {
      return { valid: false, reason: 'Command cannot be empty' };
    }

    // Check for dangerous patterns
    const dangerousPatterns = [
      /rm\s+-rf\s+\/$/,
      /rm\s+-rf\s+\*$/,
      /dd\s+if=/,
      /mkfs/,
      /fdisk/,
      /format\s+c:/i,
      /del\s+\/s/i,
      /rmdir\s+\/s/i,
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(command)) {
        return { valid: false, reason: 'Command contains dangerous patterns' };
      }
    }

    return { valid: true };
  }
}
