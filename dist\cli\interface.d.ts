import { EventEmitter } from 'events';
import type { AgentConfig } from '@/types';
export interface CLIOptions {
    interactive?: boolean;
    verbose?: boolean;
    quiet?: boolean;
    config?: string;
}
export declare class CLIInterface extends EventEmitter {
    private agent;
    private spinner;
    private verbose;
    private quiet;
    constructor(options?: CLIOptions);
    initialize(config: AgentConfig): Promise<void>;
    private setupEventHandlers;
    startInteractiveMode(): Promise<void>;
    processMessage(message: string): Promise<void>;
    private promptUser;
    private handleStreamChunk;
    private handleStreamError;
    private handleStreamComplete;
    private displayResponse;
    private showWelcome;
    private showHelp;
    private showStatus;
    private showSessions;
    private switchSession;
    private log;
    private showError;
    shutdown(): Promise<void>;
}
//# sourceMappingURL=interface.d.ts.map