import { EventEmitter } from 'events';
import type { Tool<PERSON><PERSON>, ToolResult } from '@/types';
export interface Tool {
    name: string;
    description: string;
    parameters: any;
    execute: (args: Record<string, unknown>) => Promise<ToolResult>;
}
export declare class ToolRegistry extends EventEmitter {
    private tools;
    private shellTool;
    private fileTool;
    private systemTool;
    constructor();
    initialize(): Promise<void>;
    private registerBuiltinTools;
    registerTool(tool: Tool): void;
    unregisterTool(name: string): boolean;
    getTool(name: string): Tool | undefined;
    getAvailableTools(): any[];
    executeTool(toolCall: ToolCall): Promise<ToolResult>;
    shutdown(): Promise<void>;
}
//# sourceMappingURL=registry.d.ts.map