#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import { CLIInterface } from '@/cli/interface';
import { configManager, loadConfig } from '@/utils/config';
import { logger, cleanupLogs } from '@/utils/logger';
import { Agent } from '@/core/agent';
import type { AgentConfig, AIProvider } from '@/types';

const program = new Command();

// Package information
const packageJson = require('../package.json');

program
  .name('kritrima')
  .description('AI-powered CLI tool with agentic capabilities')
  .version(packageJson.version);

// Global options
program
  .option('-c, --config <path>', 'path to configuration file')
  .option('-v, --verbose', 'enable verbose logging')
  .option('-q, --quiet', 'suppress non-error output')
  .option('--log-level <level>', 'set log level (error, warn, info, debug)', 'info');

// Interactive mode (default)
program
  .command('chat', { isDefault: true })
  .description('Start interactive chat mode')
  .option('-p, --provider <provider>', 'AI provider (openai, deepseek, ollama, azure)')
  .option('-m, --model <model>', 'AI model to use')
  .option('-k, --api-key <key>', 'API key for the provider')
  .option('-u, --base-url <url>', 'base URL for the provider')
  .option('-a, --approval <mode>', 'approval mode (suggest, auto-edit, full-auto)')
  .option('-d, --directory <path>', 'working directory')
  .action(async (options) => {
    try {
      const config = await loadConfigWithOverrides(options);
      const cli = new CLIInterface({
        interactive: true,
        verbose: program.opts().verbose,
        quiet: program.opts().quiet,
      });

      await cli.initialize(config);
      await cli.startInteractiveMode();
      await cli.shutdown();
    } catch (error) {
      console.error(chalk.red('Error:'), error instanceof Error ? error.message : error);
      process.exit(1);
    }
  });

// One-shot command execution
program
  .command('exec <message>')
  .description('Execute a single command and exit')
  .option('-p, --provider <provider>', 'AI provider (openai, deepseek, ollama, azure)')
  .option('-m, --model <model>', 'AI model to use')
  .option('-k, --api-key <key>', 'API key for the provider')
  .option('-u, --base-url <url>', 'base URL for the provider')
  .option('-a, --approval <mode>', 'approval mode (suggest, auto-edit, full-auto)')
  .option('-d, --directory <path>', 'working directory')
  .action(async (message, options) => {
    try {
      const config = await loadConfigWithOverrides(options);
      const cli = new CLIInterface({
        interactive: false,
        verbose: program.opts().verbose,
        quiet: program.opts().quiet,
      });

      await cli.initialize(config);
      await cli.processMessage(message);
      await cli.shutdown();
    } catch (error) {
      console.error(chalk.red('Error:'), error instanceof Error ? error.message : error);
      process.exit(1);
    }
  });

// Configuration management
const configCmd = program
  .command('config')
  .description('Manage configuration');

configCmd
  .command('show')
  .description('Show current configuration')
  .action(async () => {
    try {
      await configManager.load();
      const exported = await configManager.exportConfig();
      console.log(exported);
    } catch (error) {
      console.error(chalk.red('Error:'), error instanceof Error ? error.message : error);
      process.exit(1);
    }
  });

configCmd
  .command('set <key> <value>')
  .description('Set a configuration value')
  .action(async (key, value) => {
    try {
      await configManager.load();
      
      // Parse the value based on the key
      let parsedValue: any = value;
      if (['maxTokens', 'temperature'].includes(key)) {
        parsedValue = parseFloat(value);
      } else if (['interactive', 'verbose', 'quiet', 'autoSave', 'sandboxMode'].includes(key)) {
        parsedValue = value.toLowerCase() === 'true';
      }

      await configManager.update({ [key]: parsedValue });
      console.log(chalk.green(`Configuration updated: ${key} = ${parsedValue}`));
    } catch (error) {
      console.error(chalk.red('Error:'), error instanceof Error ? error.message : error);
      process.exit(1);
    }
  });

configCmd
  .command('init <provider>')
  .description('Initialize configuration for a specific provider')
  .action(async (provider: AIProvider) => {
    try {
      const template = (configManager.constructor as any).createTemplate(provider);
      await configManager.update(template);
      console.log(chalk.green(`Configuration initialized for ${provider}`));
      console.log(chalk.yellow('Please set your API key and other provider-specific settings'));
    } catch (error) {
      console.error(chalk.red('Error:'), error instanceof Error ? error.message : error);
      process.exit(1);
    }
  });

configCmd
  .command('validate')
  .description('Validate current configuration')
  .action(async () => {
    try {
      await configManager.load();
      const validation = await configManager.validateConfig();
      
      if (validation.valid) {
        console.log(chalk.green('✅ Configuration is valid'));
      } else {
        console.log(chalk.red('❌ Configuration has errors:'));
        validation.errors.forEach(error => {
          console.log(chalk.red(`  • ${error}`));
        });
        process.exit(1);
      }
    } catch (error) {
      console.error(chalk.red('Error:'), error instanceof Error ? error.message : error);
      process.exit(1);
    }
  });

// Session management
const sessionCmd = program
  .command('session')
  .description('Manage sessions');

sessionCmd
  .command('list')
  .description('List all sessions')
  .action(async () => {
    try {
      const config = await loadConfig();
      const agent = new Agent(config);
      await agent.initialize();
      
      const sessions = await agent.getSessions();
      
      if (sessions.length === 0) {
        console.log(chalk.yellow('No sessions found'));
        return;
      }
      
      console.log(chalk.bold('Sessions:'));
      console.log('ID\t\tCreated\t\t\tUpdated\t\t\tDirectory');
      console.log('--\t\t-------\t\t\t-------\t\t\t---------');
      
      sessions.forEach(session => {
        const id = session.id.slice(0, 8);
        const created = session.createdAt.toLocaleDateString();
        const updated = session.updatedAt.toLocaleDateString();
        const dir = session.workingDirectory;
        console.log(`${id}\t\t${created}\t\t${updated}\t\t${dir}`);
      });
      
      await agent.shutdown();
    } catch (error) {
      console.error(chalk.red('Error:'), error instanceof Error ? error.message : error);
      process.exit(1);
    }
  });

sessionCmd
  .command('delete <sessionId>')
  .description('Delete a session')
  .action(async (sessionId) => {
    try {
      const config = await loadConfig();
      const agent = new Agent(config);
      await agent.initialize();
      
      await agent.deleteSession(sessionId);
      console.log(chalk.green(`Session ${sessionId} deleted`));
      
      await agent.shutdown();
    } catch (error) {
      console.error(chalk.red('Error:'), error instanceof Error ? error.message : error);
      process.exit(1);
    }
  });

sessionCmd
  .command('cleanup')
  .description('Clean up old sessions')
  .option('--days <days>', 'delete sessions older than N days', '30')
  .action(async (options) => {
    try {
      const config = await loadConfig();
      const agent = new Agent(config);
      await agent.initialize();
      
      const sessionManager = (agent as any).sessionManager;
      const deletedCount = await sessionManager.cleanup(parseInt(options.days));
      
      console.log(chalk.green(`Cleaned up ${deletedCount} old sessions`));
      
      await agent.shutdown();
    } catch (error) {
      console.error(chalk.red('Error:'), error instanceof Error ? error.message : error);
      process.exit(1);
    }
  });

// Utility commands
program
  .command('logs')
  .description('Show recent logs')
  .option('--tail <lines>', 'number of lines to show', '50')
  .option('--level <level>', 'minimum log level to show')
  .action(async () => {
    try {
      // This would require implementing log reading functionality
      console.log(chalk.yellow('Log viewing not yet implemented'));
      console.log(chalk.blue('Logs are stored in ~/.kritrima/logs/'));
    } catch (error) {
      console.error(chalk.red('Error:'), error instanceof Error ? error.message : error);
      process.exit(1);
    }
  });

program
  .command('cleanup')
  .description('Clean up old logs and sessions')
  .option('--days <days>', 'clean items older than N days', '30')
  .action(async (options) => {
    try {
      const days = parseInt(options.days);
      
      // Clean up logs
      await cleanupLogs(days);
      
      // Clean up sessions
      const config = await loadConfig();
      const agent = new Agent(config);
      await agent.initialize();
      
      const sessionManager = (agent as any).sessionManager;
      const deletedSessions = await sessionManager.cleanup(days);
      
      await agent.shutdown();
      
      console.log(chalk.green(`Cleanup completed:`));
      console.log(chalk.green(`  • Cleaned up old log files`));
      console.log(chalk.green(`  • Deleted ${deletedSessions} old sessions`));
    } catch (error) {
      console.error(chalk.red('Error:'), error instanceof Error ? error.message : error);
      process.exit(1);
    }
  });

// Version and help
program
  .command('version')
  .description('Show version information')
  .action(() => {
    console.log(chalk.bold.blue('Kritrima AI CLI'));
    console.log(`Version: ${packageJson.version}`);
    console.log(`Node.js: ${process.version}`);
    console.log(`Platform: ${process.platform} ${process.arch}`);
  });

// Helper function to load config with command-line overrides
async function loadConfigWithOverrides(options: any): Promise<AgentConfig> {
  const config = await loadConfig(program.opts().config);
  
  // Apply command-line overrides
  if (options.provider) config.provider = options.provider;
  if (options.model) config.model = options.model;
  if (options.apiKey) config.apiKey = options.apiKey;
  if (options.baseUrl) config.baseUrl = options.baseUrl;
  if (options.approval) config.approvalMode = options.approval;
  if (options.directory) config.workingDirectory = options.directory;
  
  return config;
}

// Error handling
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception', error);
  console.error(chalk.red('Fatal error:'), error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled rejection', { reason, promise });
  console.error(chalk.red('Unhandled promise rejection:'), reason);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log(chalk.yellow('\nReceived SIGINT, shutting down gracefully...'));
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log(chalk.yellow('\nReceived SIGTERM, shutting down gracefully...'));
  process.exit(0);
});

// Parse command line arguments
program.parse();

// If no command provided, show help
if (!process.argv.slice(2).length) {
  program.outputHelp();
}
